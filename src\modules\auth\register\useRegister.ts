import { actionService } from '@/services'
import { IRegister } from '@/types'
import { useTranslations } from 'next-intl'

const useRegister = () => {
  const t = useTranslations()

  const handleSubmit = async (values: IRegister) => {
    actionService(
      {
        url: 'auth/register',
        method: 'post',
      },
      _,
      values
    )
    console.log('qqqqqqqqqqqqqq', values)
  }
  return {
    t,
    handleSubmit,
  }
}

export default useRegister
