'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import useRegister from './useRegister'
import { FormActionsLayout, FormInput } from '@/components/form'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { registerSchema } from '../schema'

const defaultValues = {
  first_name: '',
  last_name: '',
  phone: '',
  email: '',
  password: '',
  password_confirmation: '',
}

const Register = () => {
  const { t, handleSubmit } = useRegister()
  return (
    <>
      <h2 className="text-center text-4xl font-bold text-primary-01 mb-2 mt-12">{t('auth.register_now')}</h2>
      <p className="text-center text-[22px] text-primary-03"> {t('auth.enter_to_create_account')}</p>
      <FormWrapper schema={registerSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <div className="grid md:grid-cols-2 gap-2">
          <FormInput
            containerClassName="flex"
            name="first_name"
            label={t('label.first_name')}
            placeholder={t('label.first_name')}
          />
          <FormInput
            containerClassName="flex"
            name="last_name"
            label={t('label.last_name')}
            placeholder={t('label.last_name')}
          />
        </div>
        <FormInput name="email" type="email" label={t('label.email')} placeholder={t('label.email')} />
        <FormInput name="phone" type="number" label={t('label.phone')} placeholder={t('label.phone')} />
        <FormPasswordInput name="password" label={t('label.password')} placeholder={t('label.password')} />
        <FormPasswordInput
          name="password_confirmation"
          label={t('label.password_confirmation')}
          placeholder={t('label.password_confirmation')}
        />
        <div className="text-center">
          <Button type="submit">{t('auth.create_new_account')}</Button>
          <p className="text-primary-02 font-bold">
            {t('auth.already_have_account')}
            <Link className="text-primary-01" href={'#'}>
              {t('auth.login')}
            </Link>
          </p>
        </div>
      </FormWrapper>
    </>
  )
}

export default Register
